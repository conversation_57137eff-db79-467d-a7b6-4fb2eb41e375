var VueDemi=function(c,u,E){if(c.install)return c;if(!u)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),c;if(u.version.slice(0,4)==="2.7."){let v=function(S,A){var T,_={},U={config:u.config,use:u.use.bind(u),mixin:u.mixin.bind(u),component:u.component.bind(u),provide:function(I,F){return _[I]=F,this},directive:function(I,F){return F?(u.directive(I,F),U):u.directive(I)},mount:function(I,F){return T||(T=new u(Object.assign({propsData:A},S,{provide:Object.assign(_,S.provide)})),T.$mount(I,F),T)},unmount:function(){T&&(T.$destroy(),T=void 0)}};return U};var M=v;for(var O in u)c[O]=u[O];c.isVue2=!0,c.isVue3=!1,c.install=function(){},c.Vue=u,c.Vue2=u,c.version=u.version,c.warn=u.util.warn,c.hasInjectionContext=function(){return!!c.getCurrentInstance()},c.createApp=v}else if(u.version.slice(0,2)==="2.")if(E){for(var O in E)c[O]=E[O];c.isVue2=!0,c.isVue3=!1,c.install=function(){},c.Vue=u,c.Vue2=u,c.version=u.version,c.hasInjectionContext=function(){return!!c.getCurrentInstance()}}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(u.version.slice(0,2)==="3."){for(var O in u)c[O]=u[O];c.isVue2=!1,c.isVue3=!0,c.install=function(){},c.Vue=u,c.Vue2=void 0,c.version=u.version,c.set=function(v,S,A){return Array.isArray(v)?(v.length=Math.max(v.length,S),v.splice(S,1,A),A):(v[S]=A,A)},c.del=function(v,S){if(Array.isArray(v)){v.splice(S,1);return}delete v[S]}}else console.error("[vue-demi] Vue version "+u.version+" is unsupported.");return c}((globalThis||self).VueDemi=(globalThis||self).VueDemi||(typeof VueDemi<"u"?VueDemi:{}),(globalThis||self).Vue||(typeof Vue<"u"?Vue:void 0),(globalThis||self).VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(c,u){"use strict";function E(t,e){var n;const r=u.shallowRef();return u.watchEffect(()=>{r.value=t()},{...e,flush:(n=e?.flush)!=null?n:"sync"}),u.readonly(r)}function O(t,e){let n,r,o;const i=u.ref(!0),l=()=>{i.value=!0,o()};u.watch(t,l,{flush:"sync"});const a=typeof e=="function"?e:e.get,f=typeof e=="function"?void 0:e.set,g=u.customRef((y,h)=>(r=y,o=h,{get(){return i.value&&(n=a(),i.value=!1),r(),n},set(d){f?.(d)}}));return Object.isExtensible(g)&&(g.trigger=l),g}function M(t){return u.getCurrentScope()?(u.onScopeDispose(t),!0):!1}function v(){const t=new Set,e=o=>{t.delete(o)};return{on:o=>{t.add(o);const i=()=>e(o);return M(i),{off:i}},off:e,trigger:(...o)=>Promise.all(Array.from(t).map(i=>i(...o)))}}function S(t){let e=!1,n;const r=u.effectScope(!0);return(...o)=>(e||(n=r.run(()=>t(...o)),e=!0),n)}const A=new WeakMap,T=(t,e)=>{var n;const r=(n=u.getCurrentInstance())==null?void 0:n.proxy;if(r==null)throw new Error("provideLocal must be called in setup");A.has(r)||A.set(r,Object.create(null));const o=A.get(r);o[t]=e,u.provide(t,e)},_=(...t)=>{var e;const n=t[0],r=(e=u.getCurrentInstance())==null?void 0:e.proxy;if(r==null)throw new Error("injectLocal must be called in setup");return A.has(r)&&n in A.get(r)?A.get(r)[n]:u.inject(...t)};function U(t,e){const n=e?.injectionKey||Symbol(t.name||"InjectionState"),r=e?.defaultValue;return[(...l)=>{const a=t(...l);return T(n,a),a},()=>_(n,r)]}function I(t){let e=0,n,r;const o=()=>{e-=1,r&&e<=0&&(r.stop(),n=void 0,r=void 0)};return(...i)=>(e+=1,n||(r=u.effectScope(!0),n=r.run(()=>t(...i))),M(o),n)}function F(t,e,{enumerable:n=!1,unwrap:r=!0}={}){if(!u.isVue3&&!u.version.startsWith("2.7.")){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] extendRef only works in Vue 2.7 or above.");return}for(const[o,i]of Object.entries(e))o!=="value"&&(u.isRef(i)&&r?Object.defineProperty(t,o,{get(){return i.value},set(l){i.value=l},enumerable:n}):Object.defineProperty(t,o,{value:i,enumerable:n}));return t}function wt(t,e){return e==null?u.unref(t):u.unref(t)[e]}function mt(t){return u.unref(t)!=null}function pt(t,e){if(typeof Symbol<"u"){const n={...t};return Object.defineProperty(n,Symbol.iterator,{enumerable:!1,value(){let r=0;return{next:()=>({value:e[r++],done:r>e.length})}}}),n}else return Object.assign([...e],t)}function s(t){return typeof t=="function"?t():u.unref(t)}const vt=s;function Y(t,e){const n=e?.computedGetter===!1?u.unref:s;return function(...r){return u.computed(()=>t.apply(this,r.map(o=>n(o))))}}function bt(t,e={}){let n=[],r;if(Array.isArray(e))n=e;else{r=e;const{includeOwnProperties:o=!0}=e;n.push(...Object.keys(t)),o&&n.push(...Object.getOwnPropertyNames(t))}return Object.fromEntries(n.map(o=>{const i=t[o];return[o,typeof i=="function"?Y(i.bind(t),r):i]}))}function D(t){if(!u.isRef(t))return u.reactive(t);const e=new Proxy({},{get(n,r,o){return u.unref(Reflect.get(t.value,r,o))},set(n,r,o){return u.isRef(t.value[r])&&!u.isRef(o)?t.value[r].value=o:t.value[r]=o,!0},deleteProperty(n,r){return Reflect.deleteProperty(t.value,r)},has(n,r){return Reflect.has(t.value,r)},ownKeys(){return Object.keys(t.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return u.reactive(e)}function G(t){return D(u.computed(t))}function At(t,...e){const n=e.flat(),r=n[0];return G(()=>Object.fromEntries(typeof r=="function"?Object.entries(u.toRefs(t)).filter(([o,i])=>!r(s(i),o)):Object.entries(u.toRefs(t)).filter(o=>!n.includes(o[0]))))}const j=typeof window<"u"&&typeof document<"u",Ot=typeof WorkerGlobalScope<"u"&&globalThis instanceof WorkerGlobalScope,St=t=>typeof t<"u",Tt=t=>t!=null,Pt=(t,...e)=>{t||console.warn(...e)},Ft=Object.prototype.toString,tt=t=>Ft.call(t)==="[object Object]",Mt=()=>Date.now(),et=()=>+Date.now(),It=(t,e,n)=>Math.min(n,Math.max(e,t)),C=()=>{},Ct=(t,e)=>(t=Math.ceil(t),e=Math.floor(e),Math.floor(Math.random()*(e-t+1))+t),Rt=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),Et=kt();function kt(){var t,e;return j&&((t=window?.navigator)==null?void 0:t.userAgent)&&(/iP(?:ad|hone|od)/.test(window.navigator.userAgent)||((e=window?.navigator)==null?void 0:e.maxTouchPoints)>2&&/iPad|Macintosh/.test(window?.navigator.userAgent))}function N(t,e){function n(...r){return new Promise((o,i)=>{Promise.resolve(t(()=>e.apply(this,r),{fn:e,thisArg:this,args:r})).then(o).catch(i)})}return n}const B=t=>t();function z(t,e={}){let n,r,o=C;const i=a=>{clearTimeout(a),o(),o=C};return a=>{const f=s(t),g=s(e.maxWait);return n&&i(n),f<=0||g!==void 0&&g<=0?(r&&(i(r),r=null),Promise.resolve(a())):new Promise((y,h)=>{o=e.rejectOnCancel?h:y,g&&!r&&(r=setTimeout(()=>{n&&i(n),r=null,y(a())},g)),n=setTimeout(()=>{r&&i(r),r=null,y(a())},f)})}}function q(...t){let e=0,n,r=!0,o=C,i,l,a,f,g;!u.isRef(t[0])&&typeof t[0]=="object"?{delay:l,trailing:a=!0,leading:f=!0,rejectOnCancel:g=!1}=t[0]:[l,a=!0,f=!0,g=!1]=t;const y=()=>{n&&(clearTimeout(n),n=void 0,o(),o=C)};return d=>{const w=s(l),m=Date.now()-e,b=()=>i=d();return y(),w<=0?(e=Date.now(),b()):(m>w&&(f||!r)?(e=Date.now(),b()):a&&(i=new Promise((p,P)=>{o=g?P:p,n=setTimeout(()=>{e=Date.now(),r=!0,p(b()),y()},Math.max(0,w-m))})),!f&&!n&&(n=setTimeout(()=>r=!0,w)),r=!1,i)}}function nt(t=B){const e=u.ref(!0);function n(){e.value=!1}function r(){e.value=!0}const o=(...i)=>{e.value&&t(...i)};return{isActive:u.readonly(e),pause:n,resume:r,eventFilter:o}}const _t={mounted:u.isVue3?"mounted":"inserted",updated:u.isVue3?"updated":"componentUpdated",unmounted:u.isVue3?"unmounted":"unbind"};function rt(t){const e=Object.create(null);return n=>e[n]||(e[n]=t(n))}const jt=/\B([A-Z])/g,Nt=rt(t=>t.replace(jt,"-$1").toLowerCase()),Lt=/-(\w)/g,Wt=rt(t=>t.replace(Lt,(e,n)=>n?n.toUpperCase():""));function Z(t,e=!1,n="Timeout"){return new Promise((r,o)=>{setTimeout(e?()=>o(n):r,t)})}function Ut(t){return t}function Bt(t){let e;function n(){return e||(e=t()),e}return n.reset=async()=>{const r=e;e=void 0,r&&await r},n}function $t(t){return t()}function ot(t,...e){return e.some(n=>n in t)}function Ht(t,e){var n;if(typeof t=="number")return t+e;const r=((n=t.match(/^-?\d+\.?\d*/))==null?void 0:n[0])||"",o=t.slice(r.length),i=Number.parseFloat(r)+e;return Number.isNaN(i)?t:i+o}function Yt(t,e,n=!1){return e.reduce((r,o)=>(o in t&&(!n||t[o]!==void 0)&&(r[o]=t[o]),r),{})}function Gt(t,e,n=!1){return Object.fromEntries(Object.entries(t).filter(([r,o])=>(!n||o!==void 0)&&!e.includes(r)))}function zt(t){return Object.entries(t)}function L(t){return t||u.getCurrentInstance()}function J(...t){if(t.length!==1)return u.toRef(...t);const e=t[0];return typeof e=="function"?u.readonly(u.customRef(()=>({get:e,set:C}))):u.ref(e)}const qt=J;function Zt(t,...e){const n=e.flat(),r=n[0];return G(()=>Object.fromEntries(typeof r=="function"?Object.entries(u.toRefs(t)).filter(([o,i])=>r(s(i),o)):n.map(o=>[o,J(t,o)])))}function ct(t,e=1e4){return u.customRef((n,r)=>{let o=s(t),i;const l=()=>setTimeout(()=>{o=s(t),r()},s(e));return M(()=>{clearTimeout(i)}),{get(){return n(),o},set(a){o=a,r(),clearTimeout(i),i=l()}}})}function ut(t,e=200,n={}){return N(z(e,n),t)}function X(t,e=200,n={}){const r=u.ref(t.value),o=ut(()=>{r.value=t.value},e,n);return u.watch(t,()=>o()),r}function Jt(t,e){return u.computed({get(){var n;return(n=t.value)!=null?n:e},set(n){t.value=n}})}function it(t,e=200,n=!1,r=!0,o=!1){return N(q(e,n,r,o),t)}function K(t,e=200,n=!0,r=!0){if(e<=0)return t;const o=u.ref(t.value),i=it(()=>{o.value=t.value},e,n,r);return u.watch(t,()=>i()),o}function at(t,e={}){let n=t,r,o;const i=u.customRef((d,w)=>(r=d,o=w,{get(){return l()},set(m){a(m)}}));function l(d=!0){return d&&r(),n}function a(d,w=!0){var m,b;if(d===n)return;const p=n;((m=e.onBeforeChange)==null?void 0:m.call(e,d,p))!==!1&&(n=d,(b=e.onChanged)==null||b.call(e,d,p),w&&o())}return F(i,{get:l,set:a,untrackedGet:()=>l(!1),silentSet:d=>a(d,!1),peek:()=>l(!1),lay:d=>a(d,!1)},{enumerable:!0})}const Xt=at;function Kt(...t){if(t.length===2){const[e,n]=t;e.value=n}if(t.length===3)if(u.isVue2)u.set(...t);else{const[e,n,r]=t;e[n]=r}}function W(t,e,n={}){const{eventFilter:r=B,...o}=n;return u.watch(t,N(r,e),o)}function $(t,e,n={}){const{eventFilter:r,...o}=n,{eventFilter:i,pause:l,resume:a,isActive:f}=nt(r);return{stop:W(t,e,{...o,eventFilter:i}),pause:l,resume:a,isActive:f}}function Qt(t,e,...[n]){const{flush:r="sync",deep:o=!1,immediate:i=!0,direction:l="both",transform:a={}}=n||{},f=[],g="ltr"in a&&a.ltr||(d=>d),y="rtl"in a&&a.rtl||(d=>d);return(l==="both"||l==="ltr")&&f.push($(t,d=>{f.forEach(w=>w.pause()),e.value=g(d),f.forEach(w=>w.resume())},{flush:r,deep:o,immediate:i})),(l==="both"||l==="rtl")&&f.push($(e,d=>{f.forEach(w=>w.pause()),t.value=y(d),f.forEach(w=>w.resume())},{flush:r,deep:o,immediate:i})),()=>{f.forEach(d=>d.stop())}}function Vt(t,e,n={}){const{flush:r="sync",deep:o=!1,immediate:i=!0}=n;return Array.isArray(e)||(e=[e]),u.watch(t,l=>e.forEach(a=>a.value=l),{flush:r,deep:o,immediate:i})}function xt(t,e={}){if(!u.isRef(t))return u.toRefs(t);const n=Array.isArray(t.value)?Array.from({length:t.value.length}):{};for(const r in t.value)n[r]=u.customRef(()=>({get(){return t.value[r]},set(o){var i;if((i=s(e.replaceRef))!=null?i:!0)if(Array.isArray(t.value)){const a=[...t.value];a[r]=o,t.value=a}else{const a={...t.value,[r]:o};Object.setPrototypeOf(a,Object.getPrototypeOf(t.value)),t.value=a}else t.value[r]=o}}));return n}function Dt(t,e=!0,n){L(n)?u.onBeforeMount(t,n):e?t():u.nextTick(t)}function te(t,e){L(e)&&u.onBeforeUnmount(t,e)}function ee(t,e=!0,n){L()?u.onMounted(t,n):e?t():u.nextTick(t)}function ne(t,e){L(e)&&u.onUnmounted(t,e)}function Q(t,e=!1){function n(h,{flush:d="sync",deep:w=!1,timeout:m,throwOnTimeout:b}={}){let p=null;const x=[new Promise(H=>{p=u.watch(t,k=>{h(k)!==e&&(p?.(),H(k))},{flush:d,deep:w,immediate:!0})})];return m!=null&&x.push(Z(m,b).then(()=>s(t)).finally(()=>p?.())),Promise.race(x)}function r(h,d){if(!u.isRef(h))return n(k=>k===h,d);const{flush:w="sync",deep:m=!1,timeout:b,throwOnTimeout:p}=d??{};let P=null;const H=[new Promise(k=>{P=u.watch([t,h],([gt,He])=>{e!==(gt===He)&&(P?.(),k(gt))},{flush:w,deep:m,immediate:!0})})];return b!=null&&H.push(Z(b,p).then(()=>s(t)).finally(()=>(P?.(),s(t)))),Promise.race(H)}function o(h){return n(d=>!!d,h)}function i(h){return r(null,h)}function l(h){return r(void 0,h)}function a(h){return n(Number.isNaN,h)}function f(h,d){return n(w=>{const m=Array.from(w);return m.includes(h)||m.includes(s(h))},d)}function g(h){return y(1,h)}function y(h=1,d){let w=-1;return n(()=>(w+=1,w>=h),d)}return Array.isArray(s(t))?{toMatch:n,toContains:f,changed:g,changedTimes:y,get not(){return Q(t,!e)}}:{toMatch:n,toBe:r,toBeTruthy:o,toBeNull:i,toBeNaN:a,toBeUndefined:l,changed:g,changedTimes:y,get not(){return Q(t,!e)}}}function re(t){return Q(t)}function oe(t,e){return t===e}function ce(...t){var e;const n=t[0],r=t[1];let o=(e=t[2])!=null?e:oe;if(typeof o=="string"){const i=o;o=(l,a)=>l[i]===a[i]}return u.computed(()=>s(n).filter(i=>s(r).findIndex(l=>o(i,l))===-1))}function ue(t,e){return u.computed(()=>s(t).every((n,r,o)=>e(s(n),r,o)))}function ie(t,e){return u.computed(()=>s(t).map(n=>s(n)).filter(e))}function ae(t,e){return u.computed(()=>s(s(t).find((n,r,o)=>e(s(n),r,o))))}function le(t,e){return u.computed(()=>s(t).findIndex((n,r,o)=>e(s(n),r,o)))}function se(t,e){let n=t.length;for(;n-- >0;)if(e(t[n],n,t))return t[n]}function fe(t,e){return u.computed(()=>s(Array.prototype.findLast?s(t).findLast((n,r,o)=>e(s(n),r,o)):se(s(t),(n,r,o)=>e(s(n),r,o))))}function de(t){return tt(t)&&ot(t,"formIndex","comparator")}function he(...t){var e;const n=t[0],r=t[1];let o=t[2],i=0;if(de(o)&&(i=(e=o.fromIndex)!=null?e:0,o=o.comparator),typeof o=="string"){const l=o;o=(a,f)=>a[l]===s(f)}return o=o??((l,a)=>l===s(a)),u.computed(()=>s(n).slice(i).some((l,a,f)=>o(s(l),s(r),a,s(f))))}function ye(t,e){return u.computed(()=>s(t).map(n=>s(n)).join(s(e)))}function ge(t,e){return u.computed(()=>s(t).map(n=>s(n)).map(e))}function we(t,e,...n){const r=(o,i,l)=>e(s(o),s(i),l);return u.computed(()=>{const o=s(t);return n.length?o.reduce(r,s(n[0])):o.reduce(r)})}function me(t,e){return u.computed(()=>s(t).some((n,r,o)=>e(s(n),r,o)))}function pe(t){return Array.from(new Set(t))}function ve(t,e){return t.reduce((n,r)=>(n.some(o=>e(r,o,t))||n.push(r),n),[])}function be(t,e){return u.computed(()=>{const n=s(t).map(r=>s(r));return e?ve(n,e):pe(n)})}function Ae(t=0,e={}){let n=u.unref(t);const r=u.ref(t),{max:o=Number.POSITIVE_INFINITY,min:i=Number.NEGATIVE_INFINITY}=e,l=(h=1)=>r.value=Math.max(Math.min(o,r.value+h),i),a=(h=1)=>r.value=Math.min(Math.max(i,r.value-h),o),f=()=>r.value,g=h=>r.value=Math.max(i,Math.min(o,h));return{count:r,inc:l,dec:a,get:f,set:g,reset:(h=n)=>(n=h,g(h))}}const Oe=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[T\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/i,Se=/[YMDHhms]o|\[([^\]]+)\]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g;function Te(t,e,n,r){let o=t<12?"AM":"PM";return r&&(o=o.split("").reduce((i,l)=>i+=`${l}.`,"")),n?o.toLowerCase():o}function R(t){const e=["th","st","nd","rd"],n=t%100;return t+(e[(n-20)%10]||e[n]||e[0])}function lt(t,e,n={}){var r;const o=t.getFullYear(),i=t.getMonth(),l=t.getDate(),a=t.getHours(),f=t.getMinutes(),g=t.getSeconds(),y=t.getMilliseconds(),h=t.getDay(),d=(r=n.customMeridiem)!=null?r:Te,w={Yo:()=>R(o),YY:()=>String(o).slice(-2),YYYY:()=>o,M:()=>i+1,Mo:()=>R(i+1),MM:()=>`${i+1}`.padStart(2,"0"),MMM:()=>t.toLocaleDateString(n.locales,{month:"short"}),MMMM:()=>t.toLocaleDateString(n.locales,{month:"long"}),D:()=>String(l),Do:()=>R(l),DD:()=>`${l}`.padStart(2,"0"),H:()=>String(a),Ho:()=>R(a),HH:()=>`${a}`.padStart(2,"0"),h:()=>`${a%12||12}`.padStart(1,"0"),ho:()=>R(a%12||12),hh:()=>`${a%12||12}`.padStart(2,"0"),m:()=>String(f),mo:()=>R(f),mm:()=>`${f}`.padStart(2,"0"),s:()=>String(g),so:()=>R(g),ss:()=>`${g}`.padStart(2,"0"),SSS:()=>`${y}`.padStart(3,"0"),d:()=>h,dd:()=>t.toLocaleDateString(n.locales,{weekday:"narrow"}),ddd:()=>t.toLocaleDateString(n.locales,{weekday:"short"}),dddd:()=>t.toLocaleDateString(n.locales,{weekday:"long"}),A:()=>d(a,f),AA:()=>d(a,f,!1,!0),a:()=>d(a,f,!0),aa:()=>d(a,f,!0,!0)};return e.replace(Se,(m,b)=>{var p,P;return(P=b??((p=w[m])==null?void 0:p.call(w)))!=null?P:m})}function st(t){if(t===null)return new Date(Number.NaN);if(t===void 0)return new Date;if(t instanceof Date)return new Date(t);if(typeof t=="string"&&!/Z$/i.test(t)){const e=t.match(Oe);if(e){const n=e[2]-1||0,r=(e[7]||"0").substring(0,3);return new Date(e[1],n,e[3]||1,e[4]||0,e[5]||0,e[6]||0,r)}}return new Date(t)}function Pe(t,e="HH:mm:ss",n={}){return u.computed(()=>lt(st(s(t)),s(e),n))}function ft(t,e=1e3,n={}){const{immediate:r=!0,immediateCallback:o=!1}=n;let i=null;const l=u.ref(!1);function a(){i&&(clearInterval(i),i=null)}function f(){l.value=!1,a()}function g(){const y=s(e);y<=0||(l.value=!0,o&&t(),a(),i=setInterval(t,y))}if(r&&j&&g(),u.isRef(e)||typeof e=="function"){const y=u.watch(e,()=>{l.value&&j&&g()});M(y)}return M(f),{isActive:l,pause:f,resume:g}}function Fe(t=1e3,e={}){const{controls:n=!1,immediate:r=!0,callback:o}=e,i=u.ref(0),l=()=>i.value+=1,a=()=>{i.value=0},f=ft(o?()=>{l(),o(i.value)}:l,t,{immediate:r});return n?{counter:i,reset:a,...f}:i}function Me(t,e={}){var n;const r=u.ref((n=e.initialValue)!=null?n:null);return u.watch(t,()=>r.value=et(),e),r}function dt(t,e,n={}){const{immediate:r=!0}=n,o=u.ref(!1);let i=null;function l(){i&&(clearTimeout(i),i=null)}function a(){o.value=!1,l()}function f(...g){l(),o.value=!0,i=setTimeout(()=>{o.value=!1,i=null,t(...g)},s(e))}return r&&(o.value=!0,j&&f()),M(a),{isPending:u.readonly(o),start:f,stop:a}}function Ie(t=1e3,e={}){const{controls:n=!1,callback:r}=e,o=dt(r??C,t,e),i=u.computed(()=>!o.isPending.value);return n?{ready:i,...o}:i}function Ce(t,e={}){const{method:n="parseFloat",radix:r,nanToZero:o}=e;return u.computed(()=>{let i=s(t);return typeof i=="string"&&(i=Number[n](i,r)),o&&Number.isNaN(i)&&(i=0),i})}function Re(t){return u.computed(()=>`${s(t)}`)}function Ee(t=!1,e={}){const{truthyValue:n=!0,falsyValue:r=!1}=e,o=u.isRef(t),i=u.ref(t);function l(a){if(arguments.length)return i.value=a,i.value;{const f=s(n);return i.value=i.value===f?s(r):f,i.value}}return o?l:[i,l]}function ke(t,e,n){let r=n?.immediate?[]:[...t instanceof Function?t():Array.isArray(t)?t:s(t)];return u.watch(t,(o,i,l)=>{const a=Array.from({length:r.length}),f=[];for(const y of o){let h=!1;for(let d=0;d<r.length;d++)if(!a[d]&&y===r[d]){a[d]=!0,h=!0;break}h||f.push(y)}const g=r.filter((y,h)=>!a[h]);e(o,r,f,g,l),r=[...o]},n)}function _e(t,e,n){const{count:r,...o}=n,i=u.ref(0),l=W(t,(...a)=>{i.value+=1,i.value>=s(r)&&u.nextTick(()=>l()),e(...a)},o);return{count:i,stop:l}}function ht(t,e,n={}){const{debounce:r=0,maxWait:o=void 0,...i}=n;return W(t,e,{...i,eventFilter:z(r,{maxWait:o})})}function je(t,e,n){return u.watch(t,e,{...n,deep:!0})}function V(t,e,n={}){const{eventFilter:r=B,...o}=n,i=N(r,e);let l,a,f;if(o.flush==="sync"){const g=u.ref(!1);a=()=>{},l=y=>{g.value=!0,y(),g.value=!1},f=u.watch(t,(...y)=>{g.value||i(...y)},o)}else{const g=[],y=u.ref(0),h=u.ref(0);a=()=>{y.value=h.value},g.push(u.watch(t,()=>{h.value++},{...o,flush:"sync"})),l=d=>{const w=h.value;d(),y.value+=h.value-w},g.push(u.watch(t,(...d)=>{const w=y.value>0&&y.value===h.value;y.value=0,h.value=0,!w&&i(...d)},o)),f=()=>{g.forEach(d=>d())}}return{stop:f,ignoreUpdates:l,ignorePrevAsyncUpdates:a}}function Ne(t,e,n){return u.watch(t,e,{...n,immediate:!0})}function Le(t,e,n){const r=u.watch(t,(...o)=>(u.nextTick(()=>r()),e(...o)),n);return r}function yt(t,e,n={}){const{throttle:r=0,trailing:o=!0,leading:i=!0,...l}=n;return W(t,e,{...l,eventFilter:q(r,o,i)})}function We(t,e,n={}){let r;function o(){if(!r)return;const y=r;r=void 0,y()}function i(y){r=y}const l=(y,h)=>(o(),e(y,h,i)),a=V(t,l,n),{ignoreUpdates:f}=a;return{...a,trigger:()=>{let y;return f(()=>{y=l(Ue(t),Be(t))}),y}}}function Ue(t){return u.isReactive(t)?t:Array.isArray(t)?t.map(e=>s(e)):s(t)}function Be(t){return Array.isArray(t)?t.map(()=>{}):void 0}function $e(t,e,n){const r=u.watch(t,(o,i,l)=>{o&&(n?.once&&u.nextTick(()=>r()),e(o,i,l))},{...n,once:!1});return r}c.assert=Pt,c.autoResetRef=ct,c.bypassFilter=B,c.camelize=Wt,c.clamp=It,c.computedEager=E,c.computedWithControl=O,c.containsProp=ot,c.controlledComputed=O,c.controlledRef=Xt,c.createEventHook=v,c.createFilterWrapper=N,c.createGlobalState=S,c.createInjectionState=U,c.createReactiveFn=Y,c.createSharedComposable=I,c.createSingletonPromise=Bt,c.debounceFilter=z,c.debouncedRef=X,c.debouncedWatch=ht,c.directiveHooks=_t,c.eagerComputed=E,c.extendRef=F,c.formatDate=lt,c.get=wt,c.getLifeCycleTarget=L,c.hasOwn=Rt,c.hyphenate=Nt,c.identity=Ut,c.ignorableWatch=V,c.increaseWithUnit=Ht,c.injectLocal=_,c.invoke=$t,c.isClient=j,c.isDef=St,c.isDefined=mt,c.isIOS=Et,c.isObject=tt,c.isWorker=Ot,c.makeDestructurable=pt,c.noop=C,c.normalizeDate=st,c.notNullish=Tt,c.now=Mt,c.objectEntries=zt,c.objectOmit=Gt,c.objectPick=Yt,c.pausableFilter=nt,c.pausableWatch=$,c.promiseTimeout=Z,c.provideLocal=T,c.rand=Ct,c.reactify=Y,c.reactifyObject=bt,c.reactiveComputed=G,c.reactiveOmit=At,c.reactivePick=Zt,c.refAutoReset=ct,c.refDebounced=X,c.refDefault=Jt,c.refThrottled=K,c.refWithControl=at,c.resolveRef=qt,c.resolveUnref=vt,c.set=Kt,c.syncRef=Qt,c.syncRefs=Vt,c.throttleFilter=q,c.throttledRef=K,c.throttledWatch=yt,c.timestamp=et,c.toReactive=D,c.toRef=J,c.toRefs=xt,c.toValue=s,c.tryOnBeforeMount=Dt,c.tryOnBeforeUnmount=te,c.tryOnMounted=ee,c.tryOnScopeDispose=M,c.tryOnUnmounted=ne,c.until=re,c.useArrayDifference=ce,c.useArrayEvery=ue,c.useArrayFilter=ie,c.useArrayFind=ae,c.useArrayFindIndex=le,c.useArrayFindLast=fe,c.useArrayIncludes=he,c.useArrayJoin=ye,c.useArrayMap=ge,c.useArrayReduce=we,c.useArraySome=me,c.useArrayUnique=be,c.useCounter=Ae,c.useDateFormat=Pe,c.useDebounce=X,c.useDebounceFn=ut,c.useInterval=Fe,c.useIntervalFn=ft,c.useLastChanged=Me,c.useThrottle=K,c.useThrottleFn=it,c.useTimeout=Ie,c.useTimeoutFn=dt,c.useToNumber=Ce,c.useToString=Re,c.useToggle=Ee,c.watchArray=ke,c.watchAtMost=_e,c.watchDebounced=ht,c.watchDeep=je,c.watchIgnorable=V,c.watchImmediate=Ne,c.watchOnce=Le,c.watchPausable=$,c.watchThrottled=yt,c.watchTriggerable=We,c.watchWithFilter=W,c.whenever=$e})(this.VueUse=this.VueUse||{},VueDemi);
