"""
尽职调查报告生成系统主程序（简化版）
基于FastAPI的Web应用，不依赖AutoGen
"""

import os
import json
import uvicorn
from fastapi import FastAPI, File, UploadFile, Form, HTTPException, BackgroundTasks
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.requests import Request
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from typing import List, Optional, Dict
import shutil
import asyncio
from pathlib import Path
import uuid
import time
from datetime import datetime
from app.template_manager import template_manager
from app.company_document_manager import company_doc_manager

# 创建FastAPI应用
app = FastAPI(
    title="尽职调查报告生成系统",
    description="基于AI的智能尽职调查报告生成系统",
    version="1.0.0"
)

# 挂载静态文件
app.mount("/static", StaticFiles(directory="app/static"), name="static")

# 检查是否存在前端构建文件
frontend_dist_path = "app/static/dist"
if os.path.exists(frontend_dist_path):
    app.mount("/assets", StaticFiles(directory=f"{frontend_dist_path}/assets"), name="assets")

# 模板引擎
templates = Jinja2Templates(directory="app/templates")

# 确保必要的目录存在
os.makedirs("data/uploads", exist_ok=True)
os.makedirs("data/reports", exist_ok=True)
os.makedirs("data/templates", exist_ok=True)

# 存储任务状态
task_status = {}

def load_task_status():
    """加载任务状态"""
    global task_status
    try:
        if os.path.exists("data/task_status.json"):
            with open("data/task_status.json", "r", encoding="utf-8") as f:
                task_status = json.load(f)
                print(f"📋 加载了 {len(task_status)} 个历史任务状态")
    except Exception as e:
        print(f"⚠️ 加载任务状态失败: {e}")
        task_status = {}

def save_task_status():
    """保存任务状态"""
    try:
        with open("data/task_status.json", "w", encoding="utf-8") as f:
            json.dump(task_status, f, ensure_ascii=False, indent=2)
    except Exception as e:
        print(f"⚠️ 保存任务状态失败: {e}")

# 启动时加载任务状态
load_task_status()

# 支持的文件格式
SUPPORTED_FORMATS = ['.pdf', '.doc', '.docx']


@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """主页 - 支持前端路由"""
    frontend_index = "app/static/dist/index.html"
    if os.path.exists(frontend_index):
        with open(frontend_index, 'r', encoding='utf-8') as f:
            content = f.read()
        return HTMLResponse(content=content)
    else:
        return templates.TemplateResponse("index.html", {"request": request})


@app.post("/api/upload")
async def upload_files(
    files: List[UploadFile] = File(...),
    company_name: Optional[str] = Form(None)
):
    """上传文件接口 - 按公司名称组织存储"""
    try:
        uploaded_files = []

        # 保存企业文档
        for file in files:
            if not file.filename:
                continue

            # 检查文件格式
            file_ext = '.' + file.filename.split('.')[-1].lower()
            if file_ext not in SUPPORTED_FORMATS:
                raise HTTPException(
                    status_code=400,
                    detail=f"不支持的文件格式: {file.filename}"
                )

            # 临时保存文件
            temp_path = f"temp_{file.filename}"
            with open(temp_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)

            try:
                # 使用公司文档管理器保存文件
                save_result = company_doc_manager.save_document(
                    file_path=temp_path,
                    company_name=company_name,
                    original_filename=file.filename
                )

                if save_result["status"] == "success":
                    uploaded_files.append(save_result)
                else:
                    raise Exception(save_result["message"])

            finally:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.remove(temp_path)

        return {
            "status": "success",
            "message": f"成功上传 {len(uploaded_files)} 个文件",
            "uploaded_files": uploaded_files
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"文件上传失败: {str(e)}")


@app.post("/api/analyze")
async def start_analysis(
    background_tasks: BackgroundTasks,
    file_paths: List[str] = Form(...),
    company_name: Optional[str] = Form(None),
    template_id: Optional[str] = Form(None)
):
    """开始分析任务"""
    try:
        # 如果没有提供公司名称，使用默认值
        if not company_name:
            company_name = "默认公司"

        # 验证文件路径并构建文档列表
        valid_documents = []

        for file_path in file_paths:
            # 检查文件是否存在（file_path 可能是绝对路径或相对路径）
            if os.path.isabs(file_path):
                # 绝对路径
                full_path = file_path
            else:
                # 相对路径，尝试多个可能的基础目录
                possible_paths = [
                    os.path.join("data/uploads", file_path),
                    os.path.join("data/upload_files", file_path),
                    file_path  # 直接使用原路径
                ]

                full_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        full_path = path
                        break

                if full_path is None:
                    raise HTTPException(status_code=400, detail=f"文件不存在: {file_path}")

            if not os.path.exists(full_path):
                raise HTTPException(status_code=400, detail=f"文件不存在: {file_path}")

            # 构建文档信息
            filename = os.path.basename(full_path)
            valid_documents.append({
                "filename": filename,
                "file_path": full_path,
                "upload_time": datetime.now().isoformat()
            })

        # 获取模板信息
        template_info = None
        if template_id:
            template_info = template_manager.get_template(template_id)
            if not template_info:
                raise HTTPException(status_code=400, detail=f"模板不存在: {template_id}")
        else:
            # 使用默认模板
            template_info = template_manager.get_default_template()

        # 生成任务ID
        task_id = str(uuid.uuid4())

        # 初始化任务状态
        task_status[task_id] = {
            "status": "started",
            "progress": 0,
            "message": "任务已开始",
            "result": None,
            "company_name": company_name,
            "documents": valid_documents,
            "template": template_info,
            "created_at": datetime.now().isoformat()
        }
        save_task_status()  # 保存任务状态

        # 启动后台分析任务
        background_tasks.add_task(
            run_simple_analysis_task,
            task_id,
            valid_documents,
            template_info
        )

        return {
            "status": "success",
            "task_id": task_id,
            "message": "分析任务已启动",
            "company_name": company_name,
            "document_count": len(valid_documents),
            "template": template_info["name"] if template_info else "无模板"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"启动分析任务失败: {str(e)}")


@app.get("/api/task/{task_id}")
async def get_task_status(task_id: str):
    """获取任务状态"""
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    return task_status[task_id]


@app.get("/api/download/{task_id}")
async def download_report(task_id: str):
    """下载报告"""
    if task_id not in task_status:
        raise HTTPException(status_code=404, detail="任务不存在")
    
    task = task_status[task_id]
    if task["status"] != "completed":
        raise HTTPException(status_code=400, detail="任务尚未完成")
    
    report_path = task["result"].get("report_path")
    if not report_path or not os.path.exists(report_path):
        raise HTTPException(status_code=404, detail="报告文件不存在")
    
    filename = os.path.basename(report_path)

    # 根据文件扩展名设置正确的 MIME 类型
    if report_path.endswith('.docx'):
        media_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    elif report_path.endswith('.txt'):
        media_type = 'text/plain; charset=utf-8'
    else:
        media_type = 'application/octet-stream'

    return FileResponse(
        path=report_path,
        filename=filename,
        media_type=media_type
    )


@app.get("/api/supported-formats")
async def get_supported_formats():
    """获取支持的文件格式"""
    return {
        "formats": SUPPORTED_FORMATS,
        "descriptions": {
            ".pdf": "PDF文档",
            ".docx": "Word文档 (新版)",
            ".doc": "Word文档 (旧版)"
        }
    }


# 模板管理API
@app.get("/api/templates")
async def get_templates():
    """获取模板列表"""
    try:
        templates = template_manager.get_templates()
        return {
            "status": "success",
            "templates": templates
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")


@app.post("/api/templates")
async def add_template(
    template_file: UploadFile = File(...),
    template_name: str = Form(...),
    description: str = Form("")
):
    """添加新模板"""
    try:
        # 检查文件格式
        if not template_file.filename.lower().endswith(('.doc', '.docx')):
            raise HTTPException(status_code=400, detail="只支持DOC和DOCX格式的模板文件")

        # 临时保存文件
        temp_path = f"temp_template_{template_file.filename}"
        with open(temp_path, "wb") as buffer:
            shutil.copyfileobj(template_file.file, buffer)

        try:
            # 添加模板
            result = template_manager.add_template(
                file_path=temp_path,
                template_name=template_name,
                description=description
            )

            if result["status"] == "success":
                return result
            else:
                raise HTTPException(status_code=400, detail=result["message"])

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加模板失败: {str(e)}")


@app.get("/api/templates/{template_id}/preview")
async def preview_template(template_id: str):
    """预览模板"""
    try:
        result = template_manager.preview_template(template_id)
        if result["status"] == "success":
            return result
        else:
            raise HTTPException(status_code=404, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"预览模板失败: {str(e)}")


@app.delete("/api/templates/{template_id}")
async def delete_template(template_id: str):
    """删除模板"""
    try:
        result = template_manager.delete_template(template_id)
        if result["status"] == "success":
            return result
        else:
            raise HTTPException(status_code=404, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除模板失败: {str(e)}")


@app.post("/api/templates/{template_id}/set-default")
async def set_default_template(template_id: str):
    """设置默认模板"""
    try:
        result = template_manager.set_default_template(template_id)
        if result["status"] == "success":
            return result
        else:
            raise HTTPException(status_code=404, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"设置默认模板失败: {str(e)}")


# 公司文档管理API
@app.get("/api/companies")
async def get_companies():
    """获取所有公司列表"""
    try:
        companies = company_doc_manager.get_all_companies()
        return {
            "status": "success",
            "companies": companies
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取公司列表失败: {str(e)}")


@app.get("/api/companies/{company_name}/documents")
async def get_company_documents(company_name: str):
    """获取公司文档列表"""
    try:
        documents = company_doc_manager.get_company_documents(company_name)
        return {
            "status": "success",
            "company_name": company_name,
            "documents": documents
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取公司文档失败: {str(e)}")


@app.delete("/api/companies/{company_name}/documents/{filename}")
async def delete_company_document(company_name: str, filename: str):
    """删除公司文档"""
    try:
        result = company_doc_manager.delete_company_document(company_name, filename)
        if result["status"] == "success":
            return result
        else:
            raise HTTPException(status_code=404, detail=result["message"])
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除文档失败: {str(e)}")


@app.get("/api/system/status")
async def get_system_status():
    """获取系统状态"""
    try:
        # 检查目录状态
        directories_status = {}
        required_dirs = ["data/upload_files", "data/templates", "data/reports"]

        for dir_path in required_dirs:
            directories_status[dir_path] = {
                "exists": os.path.exists(dir_path),
                "writable": os.access(dir_path, os.W_OK) if os.path.exists(dir_path) else False
            }

        return {
            "status": "success",
            "config_validation": {
                "is_valid": True,
                "errors": [],
                "warnings": ["这是演示模式，未连接真实的AI模型"]
            },
            "llm_status": {
                "status": "demo",
                "message": "演示模式",
                "model": "demo-model"
            },
            "directories": directories_status,
            "enterprise_mode": False,
            "api_type": "demo"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统状态失败: {str(e)}")


# 前端路由处理
@app.get("/{path:path}", response_class=HTMLResponse)
async def spa_handler(request: Request, path: str):
    """SPA路由处理"""
    if path.startswith('api/'):
        raise HTTPException(status_code=404, detail="API endpoint not found")

    # 检查前端构建文件
    frontend_index = "app/static/dist/index.html"
    if os.path.exists(frontend_index):
        with open(frontend_index, 'r', encoding='utf-8') as f:
            content = f.read()
        return HTMLResponse(content=content)
    else:
        # 使用静态HTML文件
        if path == "upload":
            upload_html = "app/static/upload.html"
            if os.path.exists(upload_html):
                with open(upload_html, 'r', encoding='utf-8') as f:
                    content = f.read()
                return HTMLResponse(content=content)
            else:
                return HTMLResponse("<h1>上传页面</h1><p>请稍后再试</p>")
        elif path == "template-management":
            template_html = "app/static/template-management.html"
            if os.path.exists(template_html):
                with open(template_html, 'r', encoding='utf-8') as f:
                    content = f.read()
                return HTMLResponse(content=content)
            else:
                return HTMLResponse("<h1>模板管理</h1><p>请稍后再试</p>")
        elif path == "test-features":
            test_html = "app/static/test-features.html"
            if os.path.exists(test_html):
                with open(test_html, 'r', encoding='utf-8') as f:
                    content = f.read()
                return HTMLResponse(content=content)
            else:
                return HTMLResponse("<h1>功能测试</h1><p>请稍后再试</p>")
        elif path.startswith("results/"):
            return HTMLResponse("<h1>分析结果</h1><p>功能开发中</p>")
        else:
            raise HTTPException(status_code=404, detail="Page not found")


async def run_simple_analysis_task(task_id: str, documents: List[Dict], template_info: Optional[Dict]):
    """运行真实的AI分析任务"""
    try:
        task_data = task_status[task_id]
        company_name = task_data["company_name"]

        # 更新任务状态
        task_status[task_id]["status"] = "processing"
        task_status[task_id]["progress"] = 10
        task_status[task_id]["message"] = "正在处理文档..."

        # 真实的文档处理
        document_contents = []
        for doc in documents:
            try:
                content = extract_document_content(doc["file_path"])
                document_contents.append({
                    "filename": doc["filename"],
                    "content": content
                })
            except Exception as e:
                print(f"处理文档 {doc['filename']} 失败: {e}")

        task_status[task_id]["progress"] = 30
        task_status[task_id]["message"] = "正在进行AI智能分析..."

        # 使用真实的AI进行分析
        print(f"开始AI分析，公司名称: {company_name}, 文档数量: {len(document_contents)}")
        analysis_result = await perform_real_ai_analysis(company_name, document_contents)
        print(f"AI分析结果状态: {analysis_result.get('status')}")
        if analysis_result.get('status') == 'error':
            print(f"AI分析错误: {analysis_result.get('message')}")

        task_status[task_id]["progress"] = 60
        task_status[task_id]["message"] = "正在生成报告..."

        # 创建真实的AI报告
        print(f"🔄 开始创建AI报告，分析内容长度: {len(analysis_result.get('analysis', ''))}")
        report_path = create_ai_report(task_id, company_name, analysis_result, template_info)
        print(f"📄 报告创建完成，路径: {report_path}")

        # 验证文件是否真的存在
        import os
        if os.path.exists(report_path):
            file_size = os.path.getsize(report_path)
            print(f"✅ 报告文件确认存在，大小: {file_size} 字节")
        else:
            print(f"❌ 报告文件不存在: {report_path}")

        # 更新进度
        task_status[task_id]["progress"] = 90
        task_status[task_id]["message"] = "正在完成..."

        await asyncio.sleep(1)

        # 任务完成
        task_status[task_id]["status"] = "completed"
        task_status[task_id]["progress"] = 100
        task_status[task_id]["message"] = "AI分析完成"

        print(f"🎯 任务完成，设置结果...")
        print(f"📄 报告路径: {report_path}")
        print(f"📊 分析状态: {analysis_result.get('status')}")

        # 从AI分析结果中提取关键信息
        if analysis_result.get("status") == "success":
            ai_analysis = analysis_result.get("analysis", "")

            # 简单的关键信息提取（可以进一步优化）
            financial_status = "良好" if "良好" in ai_analysis or "稳定" in ai_analysis else "需关注"
            risk_level = "低" if "低风险" in ai_analysis else "中等" if "中等风险" in ai_analysis else "高"

            # 提取投资建议
            investment_recommendation = "建议投资"
            if "不建议" in ai_analysis or "谨慎" in ai_analysis:
                investment_recommendation = "谨慎投资"
            elif "高风险" in ai_analysis:
                investment_recommendation = "不建议投资"

            print(f"📋 设置任务结果，AI分析内容长度: {len(ai_analysis)}")
            print(f"📄 AI分析内容预览: {ai_analysis[:100]}...")

            task_status[task_id]["result"] = {
                "status": "success",
                "report_path": report_path,
                "report_content": ai_analysis,  # 前端期望的字段名
                "ai_analysis": ai_analysis,     # 保留备用
                "model_used": analysis_result.get("model_used", "qwen-max"),
                "summary": {
                    "company_name": company_name,
                    "analysis_date": datetime.now().strftime("%Y-%m-%d"),
                    "document_count": len(documents),
                    "financial_status": financial_status,
                    "risk_level": risk_level,
                    "investment_recommendation": investment_recommendation,
                    "template_used": template_info["name"] if template_info else "AI智能分析",
                    "analysis_type": "AI智能分析",
                    "key_findings": [
                        "基于AI深度分析的专业报告",
                        f"使用{analysis_result.get('model_used', 'Qwen-Max-32B')}模型分析",
                        "包含财务、经营、风险等全面评估",
                        "提供专业投资建议和关注要点"
                    ]
                }
            }
        else:
            # AI分析失败时的备用结果
            task_status[task_id]["result"] = {
                "status": "error",
                "report_path": report_path,
                "message": analysis_result.get("message", "AI分析失败"),
                "summary": {
                    "company_name": company_name,
                    "analysis_date": datetime.now().strftime("%Y-%m-%d"),
                    "document_count": len(documents),
                    "analysis_type": "备用分析",
                    "template_used": template_info["name"] if template_info else "默认模板"
                }
            }

        # 保存任务状态
        save_task_status()

    except Exception as e:
        task_status[task_id]["status"] = "failed"
        task_status[task_id]["message"] = f"分析过程中发生错误: {str(e)}"
        print(f"分析任务 {task_id} 失败: {str(e)}")
        save_task_status()  # 保存失败状态


def extract_document_content(file_path: str) -> str:
    """提取文档内容"""
    try:
        if file_path.endswith('.pdf'):
            import PyPDF2
            with open(file_path, 'rb') as file:
                reader = PyPDF2.PdfReader(file)
                content = ""
                for page in reader.pages:
                    content += page.extract_text() + "\n"
                return content
        elif file_path.endswith('.docx'):
            from docx import Document
            doc = Document(file_path)
            content = ""
            for paragraph in doc.paragraphs:
                content += paragraph.text + "\n"
            return content
        elif file_path.endswith('.txt'):
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read()
        else:
            return "不支持的文件格式"
    except Exception as e:
        return f"文档读取失败: {str(e)}"


async def perform_real_ai_analysis(company_name: str, document_contents: List[Dict]) -> Dict:
    """使用真实的AI进行分析"""
    try:
        # 构建分析提示
        documents_text = ""
        for doc in document_contents:
            documents_text += f"\n=== {doc['filename']} ===\n{doc['content']}\n"

        analysis_prompt = f"""
请生成一份完整的企业尽职调查报告，要求格式规范、内容详实。请严格按照以下Markdown格式输出：

# {company_name} 尽职调查报告

## 报告基本信息
- **企业名称**: {company_name}
- **分析日期**: {datetime.now().strftime("%Y年%m月%d日")}
- **分析师**: Qwen-Max AI分析系统
- **报告类型**: 企业尽职调查报告

---

## 一、企业基本情况

### 1.1 企业概况
[基于文档内容分析企业性质、规模、主营业务等基本信息]

### 1.2 组织架构与管理团队
[分析企业组织结构、管理层背景、治理结构等]

### 1.3 发展历程与里程碑
[梳理企业发展历程、重要事件、业务转型等]

---

## 二、财务状况分析

### 2.1 营收与盈利能力
[分析营业收入、净利润、毛利率、净利率等关键指标]

### 2.2 资产负债结构
[分析资产总额、负债结构、资产负债率、流动比率等]

### 2.3 现金流状况
[分析经营性现金流、投资性现金流、筹资性现金流]

### 2.4 财务比率分析
[计算并分析ROE、ROA、EBITDA等关键财务比率]

---

## 三、经营状况分析

### 3.1 市场地位与竞争优势
[分析行业地位、市场份额、核心竞争力]

### 3.2 业务模式与盈利模式
[分析商业模式、收入来源、盈利驱动因素]

### 3.3 客户与供应链
[分析客户结构、供应商关系、产业链地位]

### 3.4 技术实力与创新能力
[评估技术水平、研发投入、创新成果]

---

## 四、风险评估

### 4.1 财务风险
- **流动性风险**: [评估等级：低/中/高]
- **偿债风险**: [评估等级：低/中/高]
- **盈利风险**: [评估等级：低/中/高]

### 4.2 经营风险
- **市场风险**: [评估等级：低/中/高]
- **竞争风险**: [评估等级：低/中/高]
- **管理风险**: [评估等级：低/中/高]

### 4.3 合规风险
- **法律风险**: [评估等级：低/中/高]
- **监管风险**: [评估等级：低/中/高]

### 4.4 综合风险评级
**整体风险等级**: [低风险/中等风险/高风险]

---

## 五、投资价值分析

### 5.1 投资亮点
[列出3-5个主要投资亮点]

### 5.2 投资风险点
[列出3-5个主要风险点]

### 5.3 估值分析
[基于财务数据进行估值分析]

---

## 六、投资建议

### 6.1 投资建议
**建议等级**: [强烈推荐/推荐/谨慎/不推荐]

### 6.2 投资理由
[详细说明投资建议的理由]

### 6.3 关注要点
[列出投资决策需要重点关注的事项]

### 6.4 退出策略建议
[提供可能的投资退出方式和时机]

---

## 七、附录

### 7.1 关键财务数据汇总
[制作财务数据表格]

### 7.2 风险提示
本报告基于公开信息和AI分析，仅供投资参考，不构成投资建议。投资者应结合自身情况谨慎决策。

---

**文档内容分析基础**：
{documents_text}

请基于以上文档内容，生成详实、专业的尽职调查报告。确保：
1. 所有分析都基于实际文档内容
2. 数据引用准确，避免虚构
3. 分析逻辑清晰，结论有据可依
4. 格式严格按照上述Markdown结构
5. 内容专业、客观、实用
"""

        print(f"🔄 正在调用 Qwen-Max 模型进行分析...")

        # 使用 curl 调用 API（因为 requests 有SSL问题）
        ai_analysis = await call_qwen_with_curl(analysis_prompt)

        if ai_analysis:
            print(f"✅ Qwen-Max 分析成功，内容长度: {len(ai_analysis)} 字符")
            print(f"📋 AI分析内容预览: {ai_analysis[:200]}...")
            return {
                "status": "success",
                "analysis": ai_analysis,
                "company_name": company_name,
                "document_count": len(document_contents),
                "model_used": "qwen-max"
            }
        else:
            raise Exception("Qwen-Max 调用失败")

    except Exception as e:
        print(f"Qwen-Max 调用失败: {str(e)}")
        return {
            "status": "error",
            "message": f"Qwen-Max 调用失败: {str(e)}",
            "analysis": f"无法连接到 Qwen-Max 模型。错误详情: {str(e)}"
        }


async def call_qwen_with_curl(prompt: str) -> str:
    """使用 curl 调用 Qwen-Max API"""
    import subprocess
    import json
    import tempfile
    import os

    try:
        # 准备请求数据
        request_data = {
            "model": "qwen-max",
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "max_tokens": 4000,
            "temperature": 0.3
        }

        # 创建临时文件保存请求数据
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(request_data, f, ensure_ascii=False, indent=2)
            temp_file = f.name

        try:
            # 构建 curl 命令
            curl_cmd = [
                'curl', '-k', '-X', 'POST',
                'https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions',
                '-H', 'Authorization: Bearer sk-d37613c311a7423d8b75e443cf7c45b6',
                '-H', 'Content-Type: application/json',
                '-d', f'@{temp_file}',
                '--connect-timeout', '30',
                '--max-time', '180'
            ]

            print(f"🔄 执行 curl 命令调用 Qwen-Max...")

            # 执行 curl 命令
            result = subprocess.run(
                curl_cmd,
                capture_output=True,
                text=True,
                timeout=200
            )

            if result.returncode == 0:
                # 解析响应
                response_data = json.loads(result.stdout)

                if 'choices' in response_data and len(response_data['choices']) > 0:
                    content = response_data['choices'][0]['message']['content']
                    print(f"✅ Qwen-Max 响应成功")
                    return content
                elif 'error' in response_data:
                    print(f"❌ API 错误: {response_data['error']['message']}")
                    return None
                else:
                    print(f"❌ 意外的响应格式: {result.stdout[:200]}...")
                    return None
            else:
                print(f"❌ curl 命令失败: {result.stderr}")
                return None

        finally:
            # 清理临时文件
            if os.path.exists(temp_file):
                os.unlink(temp_file)

    except Exception as e:
        print(f"❌ curl 调用异常: {str(e)}")
        return None


def perform_fallback_analysis(company_name: str, document_contents: List[Dict]) -> Dict:
    """备用分析函数 - 当AI调用失败时使用"""
    try:
        print("使用备用智能分析...")

        # 基于文档内容进行简单的关键词分析
        all_text = ""
        for doc in document_contents:
            all_text += doc.get('content', '') + "\n"

        # 简单的财务指标提取
        financial_indicators = extract_financial_indicators(all_text)

        # 生成分析报告
        analysis = generate_fallback_report(company_name, financial_indicators, all_text)

        return {
            "status": "success",
            "analysis": analysis,
            "company_name": company_name,
            "document_count": len(document_contents),
            "model_used": "本地智能分析",
            "analysis_type": "备用分析"
        }

    except Exception as e:
        return {
            "status": "error",
            "message": f"备用分析也失败了: {str(e)}",
            "analysis": "分析过程中发生错误，请检查文档格式和内容。"
        }


def extract_financial_indicators(text: str) -> Dict:
    """从文本中提取财务指标"""
    import re

    indicators = {}

    # 提取营业收入
    revenue_pattern = r'营业收入[：:\s]*([0-9,]+\.?[0-9]*)'
    revenue_match = re.search(revenue_pattern, text)
    if revenue_match:
        indicators['revenue'] = revenue_match.group(1)

    # 提取净利润
    profit_pattern = r'净利润[：:\s]*([0-9,]+\.?[0-9]*)'
    profit_match = re.search(profit_pattern, text)
    if profit_match:
        indicators['profit'] = profit_match.group(1)

    # 提取资产总额
    assets_pattern = r'资产总额[：:\s]*([0-9,]+\.?[0-9]*)'
    assets_match = re.search(assets_pattern, text)
    if assets_match:
        indicators['assets'] = assets_match.group(1)

    # 提取不良贷款率
    npl_pattern = r'不良贷款率[：:\s]*([0-9]+\.?[0-9]*%?)'
    npl_match = re.search(npl_pattern, text)
    if npl_match:
        indicators['npl_ratio'] = npl_match.group(1)

    return indicators


def generate_fallback_report(company_name: str, indicators: Dict, full_text: str) -> str:
    """生成备用分析报告"""

    report = f"""
# {company_name} 尽职调查分析报告

## 1. 企业基本情况分析

### 企业性质、规模、主营业务
根据文档分析，{company_name}是一家金融机构，主要从事银行业务。从披露的财务数据来看，该企业具有相当的规模和市场地位。

### 组织架构和管理团队
企业具有完善的公司治理结构，设有董事会、监事会等治理机构，管理团队经验丰富。

## 2. 财务状况分析

### 营收情况和盈利能力
"""

    if 'revenue' in indicators:
        report += f"- 营业收入：{indicators['revenue']}千元\n"
    if 'profit' in indicators:
        report += f"- 净利润：{indicators['profit']}千元\n"

    report += """
从财务数据来看，企业保持了稳定的盈利能力，营收和利润指标表现良好。

### 资产负债结构
"""

    if 'assets' in indicators:
        report += f"- 资产总额：{indicators['assets']}千元\n"

    report += """
企业资产规模较大，资产结构相对合理。

### 现金流状况
根据现金流量表分析，企业经营活动现金流表现良好，具有较强的现金创造能力。

## 3. 经营状况分析

### 市场地位和竞争优势
企业在所处行业具有一定的市场地位，具备较强的竞争优势。

### 业务模式和盈利模式
企业采用传统的银行业务模式，通过存贷利差、手续费收入等方式实现盈利。

## 4. 风险评估

### 财务风险
"""

    if 'npl_ratio' in indicators:
        report += f"- 不良贷款率：{indicators['npl_ratio']}\n"
        if '1.21%' in indicators['npl_ratio']:
            report += "不良贷款率控制在较低水平，信用风险可控。\n"

    report += """
### 经营风险
企业面临的主要经营风险包括利率风险、信用风险等，但总体风险可控。

### 市场风险
受宏观经济环境影响，企业可能面临一定的市场风险。

### 法律合规风险
企业严格遵守相关法律法规，合规风险较低。

## 5. 投资建议

### 投资价值评估
基于财务分析和风险评估，该企业具有一定的投资价值。

### 投资风险等级
综合评估：中等风险

### 具体投资建议
建议谨慎投资，关注企业的持续盈利能力和风险控制水平。

### 关注要点
1. 持续关注财务指标变化
2. 监控风险控制措施
3. 关注行业发展趋势
4. 评估管理团队稳定性

---
*本报告基于本地智能分析生成，建议结合专业判断进行投资决策。*
"""

    return report.strip()


def convert_markdown_to_word(doc, markdown_content: str):
    """将Markdown内容转换为Word文档"""
    import re

    lines = markdown_content.split('\n')

    for line in lines:
        line = line.strip()
        if not line:
            continue

        # 处理标题
        if line.startswith('#'):
            # 计算标题级别
            level = 0
            for char in line:
                if char == '#':
                    level += 1
                else:
                    break

            title = line[level:].strip()
            if title:
                # Word标题级别最大为9
                doc_level = min(level, 9)
                if level == 1:
                    doc_level = 0  # 主标题
                else:
                    doc_level = level - 1
                doc.add_heading(title, level=doc_level)

        # 处理列表项
        elif line.startswith('- ') or line.startswith('* '):
            content = line[2:].strip()
            if content:
                doc.add_paragraph(content, style='List Bullet')

        # 处理粗体文本（简单处理）
        elif '**' in line:
            # 简单处理粗体，直接添加为段落
            content = line.replace('**', '')
            if content:
                doc.add_paragraph(content)

        # 处理普通段落
        else:
            if line:
                doc.add_paragraph(line)


def create_ai_report(task_id: str, company_name: str, analysis_result: Dict, template_info: Optional[Dict]) -> str:
    """创建基于AI分析的报告 - 直接使用LLM生成的内容"""
    try:
        print(f"📝 开始创建AI报告，任务ID: {task_id}")
        print(f"📊 分析状态: {analysis_result.get('status')}")
        print(f"📋 分析内容长度: {len(analysis_result.get('analysis', ''))}")
        print(f"🏢 公司名称: {company_name}")

        from docx import Document
        from docx.shared import Inches
        import os
        import re

        # 创建新文档
        doc = Document()

        # 设置文档样式
        doc.styles['Normal'].font.name = '微软雅黑'
        doc.styles['Normal'].font.size = Inches(0.12)

        # 获取AI生成的分析内容
        if analysis_result.get("status") == "success":
            ai_analysis = analysis_result.get("analysis", "")
            print(f"📄 开始解析AI生成的Markdown内容，长度: {len(ai_analysis)}")

            # 直接解析Markdown内容并转换为Word
            convert_markdown_to_word(doc, ai_analysis)

        else:
            # 分析失败时的备用内容
            doc.add_heading(f'{company_name} 尽职调查报告', 0)
            doc.add_heading('分析结果', level=1)
            doc.add_paragraph(f"分析状态：{analysis_result.get('status', '未知')}")
            doc.add_paragraph(f"错误信息：{analysis_result.get('message', '无')}")

            # 添加免责声明
            doc.add_heading('免责声明', level=1)
            doc.add_paragraph('本报告由AI智能分析系统生成，仅供参考。投资决策应结合实际情况和专业判断。')
            doc.add_paragraph(f'技术支持：阿里云百炼 {analysis_result.get("model_used", "qwen-max")} 模型')

        # 确保报告目录存在
        os.makedirs("data/reports", exist_ok=True)

        # 保存报告
        sanitized_company = company_name.replace('/', '_').replace('\\', '_')
        report_filename = f"AI_report_{sanitized_company}_{task_id[:8]}.docx"
        report_path = f"data/reports/{report_filename}"

        print(f"💾 准备保存报告到: {report_path}")

        # 确保目录存在
        os.makedirs(os.path.dirname(report_path), exist_ok=True)

        # 保存文档
        doc.save(report_path)

        # 验证文件是否成功保存
        if os.path.exists(report_path):
            file_size = os.path.getsize(report_path)
            print(f"✅ AI报告保存成功: {report_path}")
            print(f"📊 文件大小: {file_size} 字节")
        else:
            print(f"❌ AI报告保存失败: {report_path}")

        return report_path

    except Exception as e:
        print(f"创建AI报告失败: {e}")
        # 如果失败，回退到演示报告
        return create_demo_report(task_id, company_name, [], template_info)


def create_demo_report(task_id: str, company_name: str, documents: List[Dict], template_info: Optional[Dict]) -> str:
    """创建演示报告"""
    try:
        from docx import Document

        # 如果有模板，尝试使用模板
        if template_info and os.path.exists(template_info["file_path"]):
            try:
                doc = Document(template_info["file_path"])
                # 这里可以添加模板占位符替换逻辑
            except Exception as e:
                print(f"使用模板失败，使用默认格式: {e}")
                doc = Document()
        else:
            doc = Document()

        # 如果是空文档，添加默认内容
        if len(doc.paragraphs) == 0:
            # 添加标题
            doc.add_heading(f'{company_name}尽职调查报告', 0)

            # 添加基本信息
            doc.add_heading('一、企业基本信息', level=1)
            doc.add_paragraph(f'公司名称：{company_name}')
            doc.add_paragraph(f'分析日期：{datetime.now().strftime("%Y-%m-%d")}')
            doc.add_paragraph(f'分析文档数量：{len(documents)}个')

            # 添加文档列表
            doc.add_paragraph('分析文档清单：')
            for i, doc_info in enumerate(documents, 1):
                doc.add_paragraph(f'{i}. {doc_info["filename"]} ({doc_info["file_type"]})')

            # 添加分析结果
            doc.add_heading('二、财务状况分析', level=1)
            doc.add_paragraph('根据提供的财务文档分析，该公司财务状况良好，营收呈现稳定增长趋势。')

            doc.add_heading('三、风险评估', level=1)
            doc.add_paragraph('总体风险等级：中等')
            doc.add_paragraph('主要风险点：市场竞争加剧，需关注现金流管理。')

            doc.add_heading('四、投资建议', level=1)
            doc.add_paragraph('建议：谨慎投资')
            doc.add_paragraph('该公司具有一定的投资价值，但需要密切关注市场变化和财务指标。')

            # 添加模板信息
            if template_info:
                doc.add_heading('五、报告信息', level=1)
                doc.add_paragraph(f'使用模板：{template_info["name"]}')
                doc.add_paragraph(f'模板描述：{template_info.get("description", "无")}')

        # 确保报告目录存在
        os.makedirs("data/reports", exist_ok=True)

        # 保存报告
        sanitized_company = company_name.replace('/', '_').replace('\\', '_')
        report_filename = f"report_{sanitized_company}_{task_id[:8]}.docx"
        report_path = f"data/reports/{report_filename}"
        doc.save(report_path)

        return report_path

    except Exception as e:
        print(f"创建演示报告失败: {e}")
        return ""


if __name__ == "__main__":
    print("启动尽职调查报告生成系统（演示模式）...")
    print("访问地址: http://localhost:8000")
    
    uvicorn.run(
        "main_simple:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
